package wish_asm

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/datetime"
	"github.com/duke-git/lancet/v2/maputil"
	"github.com/gin-gonic/gin"
	"os/exec"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/ai"
	"roadtrip-api/src/components/business/holiday_biz"
	"roadtrip-api/src/components/business/plan_biz"
	"roadtrip-api/src/components/business/plan_biz/plan_asm"
	"roadtrip-api/src/components/business/qiniu_biz"
	"roadtrip-api/src/components/business/wish_biz"
	"roadtrip-api/src/components/es"
	"roadtrip-api/src/components/my_baidu"
	"roadtrip-api/src/components/my_dify"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/components/my_queue"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	es2 "roadtrip-api/src/models/es"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/utils/parallel_task"
	"roadtrip-api/src/utils/typeset"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// 心愿单风险检测
func RiskCheck(db *gorm.DB, wishId uint) error {
	var wish = new(models.Wish)
	if err := db.Joins("Ext").Preload("Todos").Preload("Medias").Take(&wish, wishId).Error; err != nil {
		return err
	}

	if wish.State != constmap.WishStateWaitReview {
		return nil
	}

	if strutil.IsBlank(wish.Cover) {
		// 视频抽取封面
		if media, ok := slice.FindBy(wish.Medias, func(index int, item models.WishMedia) bool {
			return true
		}); ok {
			if media.MediaType == constmap.WishMediaPicture {
				wish.Cover = utils.UnWrapStaticUrl(media.ResUrl)
			} else if media.MediaType == constmap.WishMediaVideo {
				// 从视频抽取封面
				videoUrl := utils.StaticUrl(media.ResUrl)

				// 创建临时文件存储封面
				tempFile, err := utils.CreateTemp("", "video_cover_*.jpg")
				if err != nil {
					my_logger.Errorf("创建临时文件失败", zap.Error(err))
				} else {
					defer tempFile.Close()

					// 抽取视频封面
					if err := extractVideoCover(videoUrl, tempFile.Name()); err != nil {
						my_logger.Errorf("抽取视频封面失败", zap.String("videoUrl", videoUrl), zap.Error(err))
					} else {
						// 生成七牛云存储路径
						coverPath := business.BuildStaticPath(constmap.WishMedia, fmt.Sprintf("cover/%s.jpg", utils.UUID()))

						// 上传到七牛云
						if err := qiniu_biz.UploadFile(tempFile.Name(), coverPath); err != nil {
							my_logger.Errorf("上传封面到七牛云失败", zap.String("coverPath", coverPath), zap.Error(err))
						} else {
							// 更新wish.Cover字段
							db.Model(&wish).Omit(clause.Associations).Updates(models.Wish{
								Cover: utils.UnWrapStaticUrl(coverPath),
							})
						}
					}
				}
			}
		}
	}

	// ========================= 内容审核 ========================

	tasks := parallel_task.NewPool(10)
	defer tasks.Release()

	riskCli := my_baidu.NewRiskClient()
	checkFails := make([]string, 0)
	var mx sync.Mutex

	//审核文本
	tasks.AddTask(func() error {
		var txts = new(strings.Builder)
		writeln := func(ss ...string) {
			slice.ForEach(ss, func(index int, item string) {
				txts.WriteString(item)
			})
			txts.WriteByte('\n')
		}
		writeln("标题:", wish.Title)
		writeln("出发地:", wish.From)
		writeln("目的地:", wish.To)
		writeln("预算:", wish.Budget)
		writeln("成员描述:", wish.MemberDesc)
		writeln("心愿描述:", wish.WishDesc)
		tagMap := wish_biz.LoadWishTags(db, *wish.TagIds.Data)
		if len(tagMap) > 0 {
			tagList := slice.Map(maputil.Values(tagMap), func(index int, item *models.WishTag) string { return item.Tag })
			writeln("标签:", strings.Join(tagList, ","))
		}

		todos := slice.Map(wish.Todos, func(index int, item models.WishTodo) string {
			return item.Todo
		})
		if len(todos) > 0 {
			writeln("心愿事项:", strings.Join(todos, "\n"))
		}

		if rsp, err := riskCli.TextCensor(txts.String()); err != nil {
			return err
		} else if rsp.ConclusionType != my_baidu.ConclusionOk {
			b, _ := json.Marshal(rsp)
			mx.Lock()
			checkFails = append(checkFails, fmt.Sprintf("文本审核失败:\n%s", string(b)))
			mx.Unlock()
		}

		return nil
	})

	//审核图片视频资源
	type videoCheckResponse struct {
		LogID                    int64                              `json:"log_id"`                   // 调用唯一ID
		ErrorCode                int64                              `json:"error_code"`               // 服务调用错误码，失败才返回，成功不返回
		ErrorMsg                 string                             `json:"error_msg"`                // 服务调用提示信息，失败才返回，成功不返回
		Conclusion               string                             `json:"conclusion"`               // 审核结果描述，可取值：合规、不合规、疑似
		ConclusionType           my_baidu.ConclusionType            `json:"conclusionType"`           // 审核结果，可取值：1 合规，2 不合规，3 疑似， 4 审核失败
		IsHitMd5                 bool                               `json:"isHitMd5"`                 // 是否命中视频黑库MD5提示
		Msg                      string                             `json:"msg"`                      // 命中MD5提示
		ConclusionTypeGroupInfos []my_baidu.ConclusionTypeGroupInfo `json:"conclusionTypeGroupInfos"` // 审核结论汇总
	}
	slice.ForEach(wish.Medias, func(index int, item models.WishMedia) {
		tasks.AddTask(func() error {
			fullUrl := utils.StaticUrl(item.ResUrl)
			if item.MediaType == constmap.WishMediaPicture {
				if rsp, err := riskCli.ImageCensor("", fullUrl); err != nil {
					return err
				} else if rsp.ConclusionType != my_baidu.ConclusionOk {
					b, _ := json.Marshal(rsp)
					mx.Lock()
					checkFails = append(checkFails, fmt.Sprintf("文件[%d]图片审核失败:\n%s", index, string(b)))
					mx.Unlock()
				}
			} else if item.MediaType == constmap.WishMediaVideo {
				if rsp, err := riskCli.VideoCensor(fullUrl); err != nil {
					return err
				} else if rsp.ConclusionType != my_baidu.ConclusionOk {
					b, _ := json.Marshal(videoCheckResponse{
						LogID:                    rsp.LogID,
						ErrorCode:                rsp.ErrorCode,
						ErrorMsg:                 rsp.ErrorMsg,
						Conclusion:               rsp.Conclusion,
						ConclusionType:           rsp.ConclusionType,
						IsHitMd5:                 rsp.IsHitMd5,
						Msg:                      rsp.Msg,
						ConclusionTypeGroupInfos: rsp.ConclusionTypeGroupInfos,
					})
					mx.Lock()
					checkFails = append(checkFails, fmt.Sprintf("文件[%d]视频审核失败:\n%s", index, string(b)))
					mx.Unlock()
				}
			}
			return nil
		})
	})

	if err := tasks.Wait(); err != nil {
		return utils.NewError(err) //需要重新运行队列消息的返回AppError
	}

	if len(checkFails) == 0 {
		_ = SetRiskCheckOk(db, wish, wish.Medias)
	} else {
		_ = db.Transaction(func(tx *gorm.DB) error {
			tx.Model(&wish).Omit(clause.Associations).Updates(models.Wish{
				State:        constmap.WishStateRejected,
				RejectReason: "内容违规",
			})
			tx.Model(&wish.Ext).Updates(models.WishExt{
				RiskCheck: strings.Join(checkFails, "\n\n"),
			})
			return nil
		})
	}

	return nil
}

func SetRiskCheckOk(db *gorm.DB, wish *models.Wish, medias []models.WishMedia) error {
	// 更新状态和封面
	updateData := models.Wish{
		State: constmap.WishStateProcessing,
	}
	db.Model(&wish).Omit(clause.Associations).Updates(updateData)
	// 审核通过后七牛文件设置为永久
	res := make([]string, 0)
	if !strutil.IsBlank(wish.Cover) {
		res = append(res, wish.Cover)
	}
	slice.ForEach(medias, func(index int, item models.WishMedia) {
		res = append(res, item.ResUrl)
	})
	slice.ForEach(res, func(index int, item string) {
		_ = qiniu_biz.SetObjectExpire(item, 0)
	})
	_ = my_queue.Light(constmap.EventWishSyncEs, gin.H{
		"ids": convertor.ToString(wish.ID),
	})
	return nil
}

func SyncAll(db *gorm.DB) error {
	var list []uint
	var nextId uint
	for {
		db.Model(&models.Wish{}).Where("id>?", nextId).Order("id ASC").Limit(100).Pluck("id", &list)
		if len(list) == 0 {
			return nil
		}
		nextId = list[len(list)-1]
		if err := syncEs(db, list); err != nil {
			return err
		}
	}
}

func Sync2EsByIds(db *gorm.DB, ids []uint) error {
	return syncEs(db, ids)
}

func syncEs(db *gorm.DB, ids []uint) error {
	if len(ids) == 0 {
		return nil
	}
	var list []*models.Wish
	db.Take(&list, ids)

	var tagIds = typeset.NewTypeSet[uint](false)
	slice.ForEach(list, func(index int, item *models.Wish) {
		tagIds.Add(*item.TagIds.Data...)
	})

	var tagMap = wish_biz.LoadWishTags(db, tagIds.Values())

	type bulkDoc struct {
		es2.Wish
		Id string
	}

	items := slice.Map(list, func(index int, item *models.Wish) bulkDoc {
		ret := bulkDoc{
			Id: utils.EsWishId(item.ID),
			Wish: es2.Wish{
				ObjId:     item.ID,
				ToZoneId:  item.ToZoneId,
				Title:     item.Title,
				Tags:      make([]string, 0),
				DayTags:   GetHolidayTags(item.DepartDate, item.ReturnDate),
				Likes:     item.Likes,
				State:     item.State,
				OpenScope: item.OpenScope,
				CreatedAt: constmap.DateTime{item.CreatedAt},
			},
		}
		for _, tagId := range *item.TagIds.Data {
			if tag, ok := tagMap[tagId]; ok {
				ret.Wish.Tags = append(ret.Wish.Tags, tag.Tag)
			}
		}
		return ret
	})

	var bulkBody strings.Builder
	for _, vk := range slice.Chunk(items, 10) {
		for _, v := range vk {
			bulkBody.WriteString(fmt.Sprintf("{\"index\":{\"_index\":\"%s\",\"_id\":\"%s\"}}\n", constmap.EsIndexWish, v.Id))
			b, _ := json.Marshal(v.Wish)
			bulkBody.Write(append(b, '\n'))
		}
	}
	if _, err := es.Bulk([]byte(bulkBody.String())); err != nil {
		my_logger.Errorf("es error", zap.Error(err))
		return err
	}
	return nil
}

func GetHolidayTags(startStr, endStr string) []string {
	if strutil.IsBlank(startStr) {
		return []string{}
	}
	var start time.Time
	var end time.Time
	times := wish_biz.ParseDepartReturn(startStr, endStr)
	switch len(times) {
	default:
		return []string{}
	case 1:
		start = times[0]
		end = utils.GetStartOfDay(datetime.EndOfMonth(start)) //指向月末那天
	case 2:
		start = times[0]
		end = times[1]
	}

	holiday, _ := holiday_biz.GetHoliday(start, end)
	holidaySet := make(map[string]beans.Holiday)
	tagSet := typeset.NewTypeSet[string](false)
	//寒假 1月中~2月中 暑假 7.7~8.31
	slice.ForEach(holiday, func(index int, item beans.Holiday) {
		if item.WorkDay == constmap.Disable {
			holidaySet[item.Date.Format(constmap.DateFmtLong)] = item
		}
	})
	for start.Unix() <= end.Unix() {
		_, month, day := start.Date()
		week := start.Weekday()
		if week == time.Saturday || week == time.Sunday {
			tagSet.Add("周末")
		}
		if month == time.January && day >= 15 || month == time.February && day <= 15 {
			tagSet.Add("寒假")
		} else if month == time.July && day >= 7 || month == time.August && day <= 31 {
			tagSet.Add("暑假")
		}
		if item, ok := holidaySet[start.Format(constmap.DateFmtLong)]; ok {
			tagSet.Add(item.Festival)
		}
		start = start.AddDate(0, 0, 1)
	}

	return tagSet.Values()
}

// 获取视频时长（秒）
func getVideoDuration(videoUrl string) (float64, error) {
	cmd := exec.Command("ffprobe", "-v", "quiet", "-show_entries", "format=duration", "-of", "csv=p=0", videoUrl)
	output, err := cmd.Output()
	if err != nil {
		return 0, fmt.Errorf("获取视频时长失败: %w", err)
	}

	durationStr := strings.TrimSpace(string(output))
	duration, err := strconv.ParseFloat(durationStr, 64)
	if err != nil {
		return 0, fmt.Errorf("解析视频时长失败: %w", err)
	}

	return duration, nil
}

// 抽取视频封面（在视频10%位置）
func extractVideoCover(videoUrl string, outputPath string) error {
	// 获取视频时长
	duration, err := getVideoDuration(videoUrl)
	if err != nil {
		return err
	}

	// 计算10%位置的时间点
	seekTime := duration * 0.1

	// 使用ffmpeg抽取封面
	cmd := exec.Command("ffmpeg", "-i", videoUrl, "-ss", fmt.Sprintf("%.2f", seekTime), "-vframes", "1", "-y", outputPath)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("抽取视频封面失败: %w", err)
	}

	return nil
}

// 心愿达成处理
func WishSuccess(db *gorm.DB, wishId uint) error {
	// 查询心愿单完整信息，预加载相关数据
	var wish models.Wish
	if err := db.Preload("Todos").Preload("Members").Take(&wish, wishId).Error; err != nil {
		my_logger.Errorf("查询心愿单失败", zap.Uint("wishId", wishId), zap.Error(err))
		return err
	}

	// 筛选出已通过审核的成员
	approvedMembers := slice.Filter(wish.Members, func(index int, member models.WishMember) bool {
		return member.State == constmap.WishMemberStateApproved
	})

	if len(approvedMembers) == 0 {
		my_logger.Infof("心愿单没有已通过审核的成员", zap.Uint("wishId", wishId))
		return nil
	}

	// 构建AI提示信息
	promptText := wish_biz.BuildWishPrompt(&wish, wish.Todos)
	my_logger.Infof("心愿单AI提示信息", zap.Uint("wishId", wishId), zap.String("prompt", promptText))

	// 调用AI生成行程计划（只生成1个模板）
	ctx := context.Background()
	travelInfo, err := my_dify.MakeWishPlan(ctx, db, promptText)
	if err != nil {
		my_logger.Errorf("生成行程计划失败", zap.Uint("wishId", wishId), zap.Error(err))
		return err
	}

	my_logger.Infof("成功生成行程计划", zap.Uint("wishId", wishId), zap.String("travelInfo", travelInfo))

	// 解析AI生成的行程内容
	aiReqId := fmt.Sprintf("dify:wish%d:%s", wishId, utils.UUID())
	chatMsg := &ai.ChatCompletionResponse{
		RequestId: aiReqId,
		Content:   travelInfo,
		Provider:  "dify",
	}

	_ = plan_biz.SetDetailCc(chatMsg)

	// 使用AI解析器解析行程内容
	aiModel := ai.NewDify()
	journeyData, err := plan_biz.ParseAIJourney(db, aiModel, chatMsg)
	if err != nil || len(journeyData.List) == 0 || !strings.Contains(travelInfo, "【行程安排】") {
		my_logger.Errorf("解析行程内容失败", zap.Uint("wishId", wishId), zap.String("aiReqid", aiReqId), zap.Error(err))
		return err
	}

	my_logger.Infof("成功解析行程内容", zap.Uint("wishId", wishId), zap.String("title", journeyData.Title))

	// 创建行程记录
	var createdPlans []*models.Plan
	var ownerPlanId uint

	// 只为第一个成员执行重操作（AI解析等）
	if len(approvedMembers) > 0 {
		firstMember := approvedMembers[0]

		// 使用公共方法构建第一个Plan（包含重操作）
		templatePlan, err := plan_asm.BuildSubmitPlan(db, plan_asm.SavePlanReq{
			UserId: firstMember.UserId,
			State:  constmap.Enable,
		}, chatMsg)
		if err != nil {
			my_logger.Errorf("构建模板行程失败", zap.Uint("wishId", wishId), zap.Uint("userId", firstMember.UserId), zap.Error(err))
			return err
		}

		// 设置心愿单关联
		templatePlan.WishId = wishId
		createdPlans = append(createdPlans, templatePlan)

		my_logger.Infof("成功构建模板行程", zap.Uint("wishId", wishId), zap.Uint("userId", firstMember.UserId))

		// 为其他成员复制Plan结构，只修改UserId
		for i := 1; i < len(approvedMembers); i++ {
			member := approvedMembers[i]

			// 使用utils.Copy深拷贝Plan结构（自动处理所有字段，ID会被重置为0）
			var memberPlan models.Plan
			if err := utils.Copy(templatePlan, &memberPlan); err != nil {
				my_logger.Errorf("拷贝行程结构失败", zap.Uint("wishId", wishId), zap.Uint("userId", member.UserId), zap.Error(err))
				return err
			}

			// 只修改需要变化的字段
			memberPlan.UserId = member.UserId
			memberPlan.WishId = wishId

			createdPlans = append(createdPlans, &memberPlan)

			my_logger.Infof("成功复制成员行程",
				zap.Uint("wishId", wishId),
				zap.Uint("userId", member.UserId))
		}
	}

	// 使用事务批量插入所有行程记录并更新心愿单状态
	err = db.Transaction(func(tx *gorm.DB) error {
		// 批量插入所有Plan记录
		if err := plan_asm.SubmitPlans(tx, createdPlans); err != nil {
			my_logger.Errorf("批量保存行程失败", zap.Uint("wishId", wishId), zap.Error(err))
			return err
		}

		// 查找发起人的行程ID
		for _, plan := range createdPlans {
			if plan.UserId == wish.UserId {
				ownerPlanId = plan.ID
				break
			}
		}

		my_logger.Infof("成功批量创建行程",
			zap.Uint("wishId", wishId),
			zap.Int("planCount", len(createdPlans)))

		// 更新心愿单：关联发起人行程ID并设置跟进状态为待跟进
		updateData := models.Wish{
			PlanId: ownerPlanId,
		}

		if err := tx.Model(&models.Wish{}).Where("id = ?", wishId).Updates(updateData).Error; err != nil {
			my_logger.Errorf("更新心愿单状态失败", zap.Uint("wishId", wishId), zap.Error(err))
			return err
		}

		my_logger.Infof("成功更新心愿单状态", zap.Uint("wishId", wishId), zap.Uint("ownerPlanId", ownerPlanId))
		return nil
	})

	if err != nil {
		return err
	}

	// TODO: 为所有已通过审核的成员发送微信模板消息
	for _, member := range approvedMembers {
		my_logger.Infof("准备为成员发送微信模板消息",
			zap.Uint("wishId", wishId),
			zap.Uint("memberId", member.ID),
			zap.Uint("userId", member.UserId),
			zap.String("realName", member.RealName))

		// 这里可以调用微信模板消息发送接口
		// 例如：wechat_biz.SendWishSuccessTemplate(member.UserId, wish.Title, travelInfo)
	}

	my_logger.Infof("心愿达成处理完成", zap.Uint("wishId", wishId), zap.Int("memberCount", len(approvedMembers)))
	return nil
}

package constmap

// SpiderDefaultZoneIds 默认长三角
const SpiderDefaultZoneIds = "7,8,9,10,11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28,29,30,31,32,71,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,6,93,103,394,395,396,397,398,399,400,401,402,403,73,249,250,251,252,253,254,255,256"

const SearchRadius = 3.0 //搜酒店、景点半径(km)

// 看广告得积分校验哈希盐
const AdIntegralPassword = "K9#mX7vL@2nQ!8pR&4w"

const (
	ActLaxin  = "laxin"   //拉新活动
	ActWeekPk = "week_pk" //行程周榜PK
)

type WishBudgetType int
type WishMemberState int
type WishState int
type WishFollowState int //跟进状态
type WishMediaType int
type LikeType int //点赞类型

const (
	WishBudgetSingle WishBudgetType = 1 //单人
	WishBudgetTeam   WishBudgetType = 2 //整体

	WishMemberStateWaitReview WishMemberState = 1 //等待审核
	WishMemberStateApproved   WishMemberState = 2 //审核通过
	WishMemberStateRejected   WishMemberState = 3 //审核拒绝

	WishStateWaitReview WishState = 1 //等待审核
	WishStateRejected   WishState = 2 //拒绝
	WishStateProcessing WishState = 3 //进行中
	WishStateSuccess    WishState = 4 //已达成
	WishStateFinished   WishState = 5 //已去过
	WishStateClosed     WishState = 6 //下架关闭

	WishFollowStateDefault        WishFollowState = 1
	WishFollowStateWait           WishFollowState = 2 //待跟进
	WishFollowStateProcessing     WishFollowState = 3 //跟进中
	WishFollowStateTransFinished  WishFollowState = 4 //达成交易
	WishFollowStateTransCancelled WishFollowState = 5 //交易取消

	WishMediaPicture WishMediaType = 1 // 图片
	WishMediaVideo   WishMediaType = 2 // 视频

	LikeTypeWish         LikeType = 1 // 心愿单点赞
	LikeTypePlan         LikeType = 2 // 行程点赞
	LikeTypeActivityPlan LikeType = 3 // 活动行程点赞
)

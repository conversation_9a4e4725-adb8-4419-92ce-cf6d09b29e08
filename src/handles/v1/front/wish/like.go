package wish

import (
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/components/my_queue"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Like(ctx *gin.Context) (any, error) {
	var in struct {
		WishId uint `form:"wish_id" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)

	// 防并发处理 - 防止同一用户对同一心愿单的并发操作
	mutexKey := fmt.Sprintf(constmap.RKMutex, "wishLike", fmt.Sprintf("%d:%d", session.UserId, in.WishId))
	unlocker, err := my_cache.Mutex(mutexKey, constmap.TimeDur5s)
	if err != nil {
		my_logger.Infof("WishLike Mutex", zap.Error(err))
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "操作过快，请稍后重试")
	}
	defer unlocker()

	// 检查心愿单是否存在
	var wish = new(models.Wish)
	if err := db.Take(&wish, in.WishId).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "心愿单不存在")
	}

	var cnt int64
	var out struct {
		Op    bool `json:"op"`    // true=点赞, false=取消点赞
		Likes int  `json:"likes"` // 最新点赞总数
	}

	// 使用事务确保原子性
	err = db.Transaction(func(tx *gorm.DB) error {
		update := map[string]any{}

		// 检查用户是否已点赞
		if tx.Model(&models.WishLike{}).Where(models.WishLike{WishId: in.WishId, UserId: session.UserId}).Count(&cnt); cnt > 0 {
			// 已点赞，执行取消点赞操作
			if err := tx.Unscoped().Delete(&models.WishLike{}, models.WishLike{WishId: in.WishId, UserId: session.UserId}).Error; err != nil {
				my_logger.Debugf("删除心愿单点赞失败", zap.Error(err))
				return utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgSystem)
			}
			update["likes"] = gorm.Expr("likes-1")
			out.Op = false
		} else {
			// 未点赞，执行点赞操作
			if err := tx.Create(&models.WishLike{WishId: in.WishId, UserId: session.UserId}).Error; err != nil {
				my_logger.Debugf("心愿单点赞失败", zap.Error(err))
				return utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgSystem)
			}
			update["likes"] = gorm.Expr("likes+1")
			out.Op = true
		}

		// 更新心愿单的点赞数
		if tx.Model(&models.Wish{}).Where("id=?", in.WishId).Updates(update).RowsAffected == 0 {
			return utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgSystem)
		}

		// 获取最新的点赞数
		tx.Model(&models.Wish{}).Where("id=?", in.WishId).Select("likes").Scan(&out.Likes)
		return nil
	})

	if err != nil {
		my_logger.Errorf("WishLike", zap.Error(err))
		return nil, utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgSystem)
	}

	// 异步同步到ES
	_ = my_queue.Light(constmap.EventWishSyncEs, gin.H{
		"ids": convertor.ToString(in.WishId),
	})

	return out, nil
}

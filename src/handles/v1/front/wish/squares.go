package wish

import (
	"roadtrip-api/src/components/business/user_biz"
	"roadtrip-api/src/components/business/wish_biz"
	"roadtrip-api/src/components/es"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	es2 "roadtrip-api/src/models/es"
	"roadtrip-api/src/utils"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// 心愿广场
func Squares(ctx *gin.Context) (any, error) {
	type sort string
	const (
		SortHot   sort = "hot"   //按热度
		SortCtime sort = "ctime" //按创建时间
	)
	var in struct {
		Sort     sort   `form:"sort"`
		ToZoneId uint   `form:"to_zone_id"`
		Tags     string `form:"tags"`
		DayTags  string `form:"day_tags"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}
	if strutil.IsBlank(string(in.Sort)) {
		in.Sort = SortHot
	}

	db := utils.GetDB(ctx)
	page, pageSize := utils.GetPage(ctx)

	// 构建基础查询条件
	mustConditions := []map[string]any{
		{
			"term": map[string]any{
				"state": constmap.WishStateProcessing,
			},
		},
		{
			"term": map[string]any{
				"open_scope": constmap.Enable,
			},
		},
	}

	// 添加目的地城市过滤条件
	if in.ToZoneId > 0 {
		mustConditions = append(mustConditions, map[string]any{
			"term": map[string]any{
				"to_zone_id": in.ToZoneId,
			},
		})
	}

	// 添加标签过滤条件（支持多选）
	if !strutil.IsBlank(in.Tags) {
		tags := strutil.SplitEx(in.Tags, ",", true)
		if len(tags) > 0 {
			mustConditions = append(mustConditions, map[string]any{
				"terms": map[string]any{
					"tags": tags,
				},
			})
		}
	}

	// 添加日期标签过滤条件（支持多选）
	if !strutil.IsBlank(in.DayTags) {
		dayTags := strutil.SplitEx(in.DayTags, ",", true)
		if len(dayTags) > 0 {
			mustConditions = append(mustConditions, map[string]any{
				"terms": map[string]any{
					"day_tags": dayTags,
				},
			})
		}
	}

	// 构建完整的查询条件
	cond := map[string]any{
		"bool": map[string]any{
			"must": mustConditions,
		},
	}

	sortCond := make([]map[string]any, 0, 1)
	if in.Sort == SortCtime {
		sortCond = append(sortCond, map[string]any{
			"created_at": map[string]any{
				"order": "desc",
			},
		})
	} else {
		sortCond = append(sortCond, map[string]any{
			"likes": map[string]any{
				"order": "desc",
			},
		})
	}

	res, err := es.Search[es2.Wish](constmap.EsIndexWish, map[string]any{
		"query": cond,
		"sort":  sortCond,
		"size":  pageSize,
		"from":  (page - 1) * pageSize,
	})
	if err != nil {
		my_logger.Errorf("es error", zap.Error(err))
		return nil, utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgSystem)
	}
	type vitem struct {
		WishId    uint     `json:"wish_id"`
		Title     string   `json:"title"`
		Cover     string   `json:"cover"`
		Nickname  string   `json:"nickname"`
		Avatar    string   `json:"avatar"`
		WishDesc  string   `json:"wish_desc"`
		Tags      []string `json:"tags"`
		Likes     int      `json:"likes"`
		CreatedAt int64    `json:"created_at"`
	}
	var out struct {
		Total int     `json:"total"`
		List  []vitem `json:"list"`
	}
	out.Total = res.Hits.Total.Value
	if len(res.Hits.Hits) == 0 {
		out.List = make([]vitem, 0)
		return out, nil
	}

	// 1. 提取ES返回的wish ID列表和tags（保持ES的排序顺序）
	type esWishData struct {
		ID   uint
		Tags []string
	}
	esWishes := slice.Map(res.Hits.Hits, func(index int, hit es.Hit[es2.Wish]) esWishData {
		return esWishData{
			ID:   hit.Source.ObjId,
			Tags: hit.Source.Tags,
		}
	})

	ids := slice.Map(esWishes, func(index int, item esWishData) uint {
		return item.ID
	})

	// 2. 批量查询wish完整数据
	wishMap := wish_biz.LoadWishes(db, ids)

	// 3. 收集用户ID
	userIds := make([]uint, 0, len(wishMap))
	for _, wish := range wishMap {
		userIds = append(userIds, wish.UserId)
	}

	// 4. 批量查询用户数据
	userMap := user_biz.LoadUsers(db, userIds)

	// 5. 按ES顺序组装输出数据
	out.List = make([]vitem, 0, len(esWishes))
	for _, esWish := range esWishes {
		wish, wishExists := wishMap[esWish.ID]
		if !wishExists {
			continue // 容错：查不到直接跳过
		}

		user, userExists := userMap[wish.UserId]

		item := vitem{
			WishId:    wish.ID,
			Title:     wish.Title,
			Cover:     utils.StaticUrl(wish.Cover),
			WishDesc:  wish.WishDesc,
			Tags:      esWish.Tags, // 直接使用ES结果中的tags
			Likes:     wish.Likes,
			CreatedAt: wish.CreatedAt.Unix(),
		}

		if userExists {
			item.Nickname = user.Nickname
			item.Avatar = utils.AvatarUrl(user.Avatar)
		}

		out.List = append(out.List, item)
	}

	return out, nil
}
